<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Appointment;
use App\Models\Purchase;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AppointmentApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $assistant;
    protected $representative;
    protected $token;

    protected function setUp(): void
    {
        parent::setUp();

        $this->assistant = User::factory()->create([
            'role' => 'assistant',
            'is_activated' => true,
        ]);

        $this->representative = User::factory()->create([
            'role' => 'representative',
            'is_activated' => true,
        ]);

        $this->token = $this->assistant->createToken('test-token')->plainTextToken;
    }

    public function test_can_get_appointments_list()
    {
        Appointment::factory()->count(3)->create([
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/v1/appointments');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        '*' => [
                            'id',
                            'assistant_id',
                            'representative_id',
                            'client_name',
                            'client_phone',
                            'client_address',
                            'source',
                            'date_time',
                            'notes',
                            'items_collection',
                            'appointment_type',
                        ],
                    ],
                ]);
    }

    public function test_can_create_appointment()
    {
        $appointmentData = [
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
            'client_name' => 'John Doe',
            'client_phone' => '1234567890',
            'client_address' => '123 Main St',
            'source' => 'outbound',
            'date_time' => now()->addDay()->toISOString(),
            'notes' => 'Test appointment',
            'items_collection' => ['bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)'],
            'appointment_type' => 'announced',
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v1/appointments', $appointmentData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'assistant_id',
                        'representative_id',
                        'client_name',
                        'client_phone',
                        'client_address',
                        'source',
                        'date_time',
                        'notes',
                        'items_collection',
                        'appointment_type',
                    ],
                ]);

        $this->assertDatabaseHas('appointments', [
            'clientName' => 'John Doe',
            'clientPhone' => '1234567890',
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
        ]);
    }

    public function test_can_get_single_appointment()
    {
        $appointment = Appointment::factory()->create([
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson("/api/v1/appointments/{$appointment->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'assistant_id',
                        'representative_id',
                        'client_name',
                        'client_phone',
                        'client_address',
                        'source',
                        'date_time',
                        'notes',
                        'items_collection',
                        'appointment_type',
                        'assistant',
                        'representative',
                    ],
                ]);
    }

    public function test_can_update_appointment()
    {
        $appointment = Appointment::factory()->create([
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
        ]);

        $updateData = [
            'client_name' => 'Updated Name',
            'notes' => 'Updated notes',
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->putJson("/api/v1/appointments/{$appointment->id}", $updateData);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Appointment updated successfully',
                ]);

        $this->assertDatabaseHas('appointments', [
            'id' => $appointment->id,
            'clientName' => 'Updated Name',
            'notes' => 'Updated notes',
        ]);
    }

    public function test_can_delete_appointment()
    {
        $appointment = Appointment::factory()->create([
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->deleteJson("/api/v1/appointments/{$appointment->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Appointment deleted successfully',
                ]);

        $this->assertDatabaseMissing('appointments', [
            'id' => $appointment->id,
        ]);
    }

    public function test_can_get_my_appointments()
    {
        // Create appointments for this assistant
        Appointment::factory()->count(2)->create([
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
        ]);

        // Create appointment for another assistant
        $otherAssistant = User::factory()->create(['role' => 'assistant']);
        Appointment::factory()->create([
            'assistant_id' => $otherAssistant->id,
            'representative_id' => $this->representative->id,
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/v1/appointments/my-appointments');

        $response->assertStatus(200)
                ->assertJsonCount(2, 'data');
    }

    public function test_can_create_rdv_as_assistant()
    {
        // Create assignment between assistant and representative
        \App\Models\AssistantRepresentativeAssignment::create([
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
        ]);

        $rdvData = [
            'representative_id' => $this->representative->id,
            'client_name' => 'Jean Dupont',
            'client_phone' => '0123456789',
            'client_address' => '123 Rue de la Paix, 75001 Paris',
            'source' => 'outbound',
            'date_time' => now()->addDay()->toISOString(),
            'notes' => 'Client intéressé par la vente de bijoux anciens',
            'items_collection' => [
                'bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)',
                'montres'
            ],
            'appointment_type' => 'announced',
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v1/appointments/rdv', $rdvData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'assistant_id',
                        'representative_id',
                        'client_name',
                        'client_phone',
                        'client_address',
                        'source',
                        'date_time',
                        'notes',
                        'items_collection',
                        'appointment_type',
                        'assistant',
                        'representative',
                    ],
                ])
                ->assertJson([
                    'success' => true,
                    'message' => 'RDV created successfully',
                ]);

        $this->assertDatabaseHas('appointments', [
            'clientName' => 'Jean Dupont',
            'clientPhone' => '0123456789',
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
            'source' => 'outbound',
            'appointment_type' => 'announced',
        ]);
    }

    public function test_cannot_create_rdv_without_assignment()
    {
        // Don't create assignment between assistant and representative

        $rdvData = [
            'representative_id' => $this->representative->id,
            'client_name' => 'Jean Dupont',
            'client_phone' => '0123456789',
            'client_address' => '123 Rue de la Paix, 75001 Paris',
            'source' => 'outbound',
            'date_time' => now()->addDay()->toISOString(),
            'notes' => 'Client intéressé par la vente de bijoux anciens',
            'items_collection' => [
                'bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)'
            ],
            'appointment_type' => 'announced',
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v1/appointments/rdv', $rdvData);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'You are not assigned to this representative',
                ]);
    }

    public function test_representative_cannot_create_rdv()
    {
        $representativeToken = $this->representative->createToken('test-token')->plainTextToken;

        $rdvData = [
            'representative_id' => $this->representative->id,
            'client_name' => 'Jean Dupont',
            'client_phone' => '0123456789',
            'client_address' => '123 Rue de la Paix, 75001 Paris',
            'source' => 'outbound',
            'date_time' => now()->addDay()->toISOString(),
            'notes' => 'Client intéressé par la vente de bijoux anciens',
            'items_collection' => [
                'bijoux de valeurs casses, depareilles ou non (argent, or et plaque or)'
            ],
            'appointment_type' => 'announced',
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $representativeToken,
        ])->postJson('/api/v1/appointments/rdv', $rdvData);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Only assistants can create appointments',
                ]);
    }

    public function test_representative_can_get_dashboard_stats()
    {
        $representativeToken = $this->representative->createToken('test-token')->plainTextToken;

        // Create today's appointments
        $todayAppointment1 = Appointment::factory()->create([
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
            'dateTime' => Carbon::today()->addHours(10),
        ]);

        $todayAppointment2 = Appointment::factory()->create([
            'assistant_id' => $this->assistant->id,
            'representative_id' => $this->representative->id,
            'dateTime' => Carbon::today()->addHours(14),
        ]);

        // Create a purchase for one appointment (realized)
        Purchase::factory()->create([
            'appointment_id' => $todayAppointment1->id,
            'status' => 'purchased',
            'resale_price' => 150.00,
            'benefit' => 50.00,
        ]);

        // Create a non-purchase for the other appointment
        Purchase::factory()->create([
            'appointment_id' => $todayAppointment2->id,
            'status' => 'not_purchased',
            'resale_price' => 0,
            'benefit' => 0,
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $representativeToken,
        ])->getJson('/api/v1/appointments/representative-dashboard-stats');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'todays_appointments',
                        'todays_realized_appointments',
                        'todays_revenue',
                        'todays_benefit',
                        'realization_rate',
                        'date',
                    ]
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'todays_appointments' => 2,
                        'todays_realized_appointments' => 1,
                        'todays_revenue' => 150.0,
                        'todays_benefit' => 50.0,
                        'realization_rate' => 50.0,
                        'date' => Carbon::today()->toDateString(),
                    ]
                ]);
    }

    public function test_assistant_cannot_access_representative_dashboard_stats()
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/v1/appointments/representative-dashboard-stats');

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Only representatives can access dashboard stats',
                ]);
    }
}
